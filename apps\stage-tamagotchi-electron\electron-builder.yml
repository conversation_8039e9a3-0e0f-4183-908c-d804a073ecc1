appId: ai.moeru.airi
productName: AIRI
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!vite.config.{js,ts,mjs,cjs}'
  - '!uno.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.ts,.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json}'
asarUnpack:
  - resources/**
win:
  executableName: AIRI
nsis:
  artifactName: ${productName}-v${version}-windows-${arch}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  deleteAppDataOnUninstall: true
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
  executableName: AIRI
dmg:
  artifactName: ${productName}-v${version}-darwin-${arch}.${ext}
linux:
  target:
    - AppImage
  maintainer: moeru.ai
  category: Utility
  executableName: AIRI
  artifactName: ${productName}-v${version}-linux-${arch}.${ext}
appImage:
  artifactName: ${productName}-v${version}-linux-${arch}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
