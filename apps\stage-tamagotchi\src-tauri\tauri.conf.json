{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "AIRI", "version": "../package.json", "identifier": "ai.moeru.airi-tamagotchi", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "pnpm run dev", "beforeBuildCommand": "pnpm run build"}, "app": {"security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": ["dmg", "appimage", "deb", "rpm", "nsis"], "icon": ["icons/32x32.png", "icons/64x64.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": {"infoplist/**/*": "./"}, "macOS": {"entitlements": "./Entitlements.plist"}, "linux": {"deb": {"depends": ["libatk-bridge2.0-0", "libatk1.0-0", "libatomic1", "libatspi2.0-0", "libblkid1", "libbrotli1", "libbsd0", "libbz2-1.0", "libc6", "libcairo-gobject2", "libcairo2", "libcap2", "libcom-err2", "libdatrie1", "libdbus-1-3", "libdrm2", "libdw1", "libegl1", "libelf1", "libenchant-2-2", "libepoxy0", "libevdev2", "libexpat1", "libffi8", "libfontconfig1", "libfreetype6", "libfribidi0", "libgbm1", "libgcc-s1", "libgcrypt20", "libgdk-pixbuf-2.0-0", "libgl1", "libglib2.0-0", "libglvnd0", "libglx0", "libgpg-error0", "libgraphite2-3", "libgssapi-krb5-2", "libgstreamer-gl1.0-0", "libgstreamer-plugins-base1.0-0", "libgstreamer1.0-0", "libgtk-3-0", "libgudev-1.0-0", "libharfbuzz-icu0", "libharfbuzz0b", "libhyphen0", "libicu74", "libidn2-0", "libjavascriptcoregtk-4.1-0", "libjpeg-turbo8", "libk5crypto3", "libkeyutils1", "libkrb5-3", "libkrb5support0", "liblcms2-2", "liblz4-1", "liblzma5", "libmanette-0.2-0", "libmd0", "libmount1", "libnghttp2-14", "liborc-0.4-0", "libpango-1.0-0", "libpangocairo-1.0-0", "libpangoft2-1.0-0", "libpcre2-8-0", "libpixman-1-0", "libpng16-16", "libpsl5", "libseccomp2", "libsecret-1-0", "libselinux1", "libsharpyuv0", "libsqlite3-0", "libsoup-3.0-0", "libssl3", "libstdc++6", "libsystemd0", "libtasn1-6", "libthai0", "libudev1", "libunistring5", "libunwind8", "libwayland-client0", "libwayland-cursor0", "libwayland-egl1", "libwayland-server0", "libwebkit2gtk-4.1-0", "libwebp7", "libwebpdemux2", "libwebpmux3", "libwoff1", "libx11-6", "libx11-xcb1", "libxau6", "libxcb-render0", "libxcb-shm0", "libxcb1", "libxcomposite1", "libxcursor1", "libxdamage1", "libxdmcp6", "libxext6", "libxfixes3", "libxi6", "libxinerama1", "libxkbcommon0", "libxml2", "libxrandr2", "libxrender1", "libxslt1.1", "libxtst6", "libzstd1"]}, "rpm": {"depends": ["at-spi2-atk", "at-spi2-core", "atk", "bzip2-libs", "cairo", "cairo-gobject", "dbus-libs", "elfutils-libelf", "elfutils-libs", "enchant2", "expat", "flac-libs", "flite", "fontconfig", "freetype", "<PERSON><PERSON><PERSON><PERSON>", "gdk-pixbuf2", "glib2", "glibc", "gmp", "gnutls", "graphite2", "gsm", "gstreamer1", "gstreamer1-plugins-bad-free-libs", "gstreamer1-plugins-base", "gtk3", "harfbuzz", "harfbuzz-icu", "<PERSON><PERSON>i", "highway", "hyphen", "javascriptcoregtk4.1", "json-glib", "keyutils-libs", "krb5-libs", "lame-libs", "lcms2", "libX11", "libX11-xcb", "libXau", "libXcomposite", "libXcursor", "libXdamage", "libXext", "libXfixes", "libXi", "libXinerama", "libXrandr", "libXrender", "libXtst", "libaom", "libasyncns", "liba<PERSON>f", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "l<PERSON><PERSON><PERSON><PERSON>", "libcap", "libcloudproviders", "libcom_err", "libdatrie", "libdav1d", "libdrm", "libepoxy", "libevdev", "libffi", "libgcc", "libgcrypt", "libglvnd", "libglvnd-egl", "libglvnd-glx", "libgpg-error", "libgudev", "libicu", "libidn2", "libjpeg-turbo", "libjxl", "lib<PERSON>ette", "libmount", "libnghttp2", "libogg", "libpng", "libpsl", "libseccomp", "libsecret", "libselinux", "libsndfile", "libsoup3", "libstdc++", "libtasn1", "lib<PERSON>i", "libtinysparql", "libunistring", "libunwind", "libvorbis", "libwayland-client", "libwayland-cursor", "libwayland-egl", "libwayland-server", "libwebp", "libxcb", "libxkbcommon", "libxml2", "libxslt", "libyuv", "libzstd", "mesa-libgbm", "mpg123-libs", "nettle", "openssl-libs", "opus", "orc", "p11-kit", "pango", "pcre2", "pixman", "pulseaudio-libs", "rav1e-libs", "sqlite-libs", "svt-av1-libs", "systemd-libs", "webkit2gtk4.1", "woff2", "xz-libs", "zlib-ng-compat"]}}}}