---
title: Introduction
description: Get to know the UI of Project AIRI
---

### TL;DR

Think of us as

- open source re-creation of [Neuro-sama](https://www.youtube.com/@Neurosama)
- open source alternative to [Grok Companion](https://news.ycombinator.com/item?id=44566355)
- a Live2D, VRM (3D), and role playing with gaming, and application
awareness specialized [SillyTavern](https://github.com/SillyTavern/SillyTavern)
alternative.

Have you dreamed about having a cyber living being (cyber waifu / husbando),
or digital companion that could play with and talk to you?

With the power of modern large language models, platforms like
[Character.ai (a.k.a. c.ai)](https://character.ai) and
[JanitorA<PERSON>](https://janitorai.com/), or applications like
[<PERSON>llyTavern](https://github.com/SillyTavern/SillyTavern) is already a well-enough
solution for chat based, or visual adventure game like experience.

> But, what about the abilities to play games? And see what you are coding
> at? Chatting while playing games, watching videos, and capable of doing many
> other things.

Perhaps you know [<PERSON><PERSON><PERSON>-sama](https://www.youtube.com/@Neurosama) already, she is
currently the best companion capable of playing games, chatting, and interacting
with you and the participants (in VTuber community), some call this kind of being,
"digital human" too. **Sadly, it's not open sourced, you cannot interact with her after she went offline from live stream**.

Therefore, this project, AIRI, offers another possibility here:
**let you own your digital life, cyber living, easily, anywhere, anytime**.

## Getting started

We do support both Web and Desktop.

<div flex gap-2 w-full justify-center text-xl>
  <div w-full flex flex-col items-center gap-2 border="2 solid gray-500/10" rounded-lg px-2 pt-6 pb-4>
    <div flex items-center gap-2 text-5xl>
      <div i-lucide:app-window />
    </div>
    <span>Web</span>
    <a href="https://airi.moeru.ai/" target="_blank" decoration-none class="text-primary-900 dark:text-primary-400 text-base not-prose bg-primary-400/10 dark:bg-primary-600/10 block px-4 py-2 rounded-lg active:scale-95 transition-all duration-200 ease-in-out">
      Open
    </a>
  </div>
  <div w-full flex flex-col items-center gap-2 border="2 solid gray-500/10" rounded-lg px-2 pt-6 pb-4>
    <div flex items-center gap-2 text-5xl>
      <div i-lucide:laptop />
      /
      <div i-lucide:computer />
    </div>
    <span>Desktop</span>
    <a href="https://github.com/moeru-ai/airi/releases/latest" target="_blank" decoration-none class="text-primary-900 dark:text-primary-400 text-base not-prose bg-primary-400/10 dark:bg-primary-600/10 block px-4 py-2 rounded-lg active:scale-95 transition-all duration-200 ease-in-out">
      Download
    </a>
  </div>
</div>

The web version offers easy access from anywhere, even on mobile devices.
Desktop suits for more advanced usage such as VTuber streaming, computer use
and accessing to local LLMs where you don't need to pay for massive amount
of tokens for running AIRI.

<div flex gap-2 w-full flex-col justify-center text-base>
  <a href="../overview/guide/tamagotchi/" w-full flex items-center gap-2 border="2 solid gray-500/10" rounded-lg px-4 py-2>
    <div w-full flex items-center gap-2>
      <div flex items-center gap-2 text-2xl>
        <div i-lucide:laptop />
      </div>
      <span>Desktop</span>
    </div>
    <div decoration-none class="text-gray-900 dark:text-gray-200 text-base not-prose rounded-lg active:scale-95 transition-all duration-200 ease-in-out text-nowrap">
      How to use?
    </div>
  </a>
  <a href="../overview/guide/web/" w-full flex items-center gap-2 border="2 solid gray-500/10" rounded-lg px-4 py-2>
    <div w-full flex items-center gap-2>
      <div flex items-center gap-2 text-2xl>
        <div i-lucide:app-window />
      </div>
      <span>Web</span>
    </div>
    <div class="text-gray-900 dark:text-gray-200 text-base not-prose rounded-lg active:scale-95 transition-all duration-200 ease-in-out text-nowrap">
      How to use?
    </div>
  </a>
</div>

## Contributing

For guides that help you understand how to contribute to this project, please refer to [Contributing](../overview/contributing/) page.

For references Contributing help you design and improve the UI of Project AIRI, please refer to [Design Guidelines](../overview/contributing/design-guidelines/resources) page.
