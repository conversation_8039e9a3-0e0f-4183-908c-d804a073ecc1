// rdev enums
export enum KeyCode {
  // Alt key on Linux and Windows (option key on macOS)
  Alt = 'Alt',
  AltGr = 'AltGr',
  Backspace = 'Backspace',
  CapsLock = 'CapsLock',
  ControlLeft = 'ControlLeft',
  ControlRight = 'ControlRight',
  Delete = 'Delete',
  DownArrow = 'DownArrow',
  End = 'End',
  Escape = 'Escape',
  F1 = 'F1',
  F10 = 'F10',
  F11 = 'F11',
  F12 = 'F12',
  F13 = 'F13',
  F14 = 'F14',
  F15 = 'F15',
  F16 = 'F16',
  F17 = 'F17',
  F18 = 'F18',
  F19 = 'F19',
  F20 = 'F20',
  F21 = 'F21',
  F22 = 'F22',
  F23 = 'F23',
  F24 = 'F24',
  F2 = 'F2',
  F3 = 'F3',
  F4 = 'F4',
  F5 = 'F5',
  F6 = 'F6',
  F7 = 'F7',
  F8 = 'F8',
  F9 = 'F9',
  Home = 'Home',
  LeftArrow = 'LeftArrow',
  /// also known as "windows", "super", and "command"
  MetaLeft = 'MetaLeft',
  /// also known as "windows", "super", and "command"
  MetaRight = 'MetaRight',
  PageDown = 'PageDown',
  PageUp = 'PageUp',
  Return = 'Return',
  RightArrow = 'RightArrow',
  ShiftLeft = 'ShiftLeft',
  ShiftRight = 'ShiftRight',
  Space = 'Space',
  Tab = 'Tab',
  UpArrow = 'UpArrow',
  PrintScreen = 'PrintScreen',
  ScrollLock = 'ScrollLock',
  Pause = 'Pause',
  NumLock = 'NumLock',
  BackQuote = 'BackQuote',
  Num1 = 'Num1',
  Num2 = 'Num2',
  Num3 = 'Num3',
  Num4 = 'Num4',
  Num5 = 'Num5',
  Num6 = 'Num6',
  Num7 = 'Num7',
  Num8 = 'Num8',
  Num9 = 'Num9',
  Num0 = 'Num0',
  Minus = 'Minus',
  Equal = 'Equal',
  KeyQ = 'KeyQ',
  KeyW = 'KeyW',
  KeyE = 'KeyE',
  KeyR = 'KeyR',
  KeyT = 'KeyT',
  KeyY = 'KeyY',
  KeyU = 'KeyU',
  KeyI = 'KeyI',
  KeyO = 'KeyO',
  KeyP = 'KeyP',
  LeftBracket = 'LeftBracket',
  RightBracket = 'RightBracket',
  KeyA = 'KeyA',
  KeyS = 'KeyS',
  KeyD = 'KeyD',
  KeyF = 'KeyF',
  KeyG = 'KeyG',
  KeyH = 'KeyH',
  KeyJ = 'KeyJ',
  KeyK = 'KeyK',
  KeyL = 'KeyL',
  SemiColon = 'SemiColon',
  Quote = 'Quote',
  BackSlash = 'BackSlash',
  IntlBackslash = 'IntlBackslash',
  KeyZ = 'KeyZ',
  KeyX = 'KeyX',
  KeyC = 'KeyC',
  KeyV = 'KeyV',
  KeyB = 'KeyB',
  KeyN = 'KeyN',
  KeyM = 'KeyM',
  Comma = 'Comma',
  Dot = 'Dot',
  Slash = 'Slash',
  Insert = 'Insert',
  KpReturn = 'KpReturn',
  KpMinus = 'KpMinus',
  KpPlus = 'KpPlus',
  KpMultiply = 'KpMultiply',
  KpDivide = 'KpDivide',
  Kp0 = 'Kp0',
  Kp1 = 'Kp1',
  Kp2 = 'Kp2',
  Kp3 = 'Kp3',
  Kp4 = 'Kp4',
  Kp5 = 'Kp5',
  Kp6 = 'Kp6',
  Kp7 = 'Kp7',
  Kp8 = 'Kp8',
  Kp9 = 'Kp9',
  KpDelete = 'KpDelete',
  Function = 'Function',
  VolumeUp = 'VolumeUp',
  VolumeDown = 'VolumeDown',
  VolumeMute = 'VolumeMute',
  BrightnessUp = 'BrightnessUp',
  BrightnessDown = 'BrightnessDown',
  PreviousTrack = 'PreviousTrack',
  PlayPause = 'PlayPause',
  PlayCd = 'PlayCd',
  NextTrack = 'NextTrack',
  Unknown = 'Unknown',
}

// Follows the exact order
// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_code_values#code_values_on_windows
export const mapKeyCode: Record<KeyCode, string> = {
  [KeyCode.Escape]: 'Escape',
  [KeyCode.Kp0]: 'Digit0',
  [KeyCode.Kp1]: 'Digit1',
  [KeyCode.Kp2]: 'Digit2',
  [KeyCode.Kp3]: 'Digit3',
  [KeyCode.Kp4]: 'Digit4',
  [KeyCode.Kp5]: 'Digit5',
  [KeyCode.Kp6]: 'Digit6',
  [KeyCode.Kp7]: 'Digit7',
  [KeyCode.Kp8]: 'Digit8',
  [KeyCode.Kp9]: 'Digit9',
  [KeyCode.Minus]: 'Minus',
  [KeyCode.Equal]: 'Equal',
  [KeyCode.Backspace]: 'Backspace',
  [KeyCode.Tab]: 'Tab',
  [KeyCode.KeyQ]: 'KeyQ',
  [KeyCode.KeyW]: 'KeyW',
  [KeyCode.KeyE]: 'KeyE',
  [KeyCode.KeyR]: 'KeyR',
  [KeyCode.KeyT]: 'KeyT',
  [KeyCode.KeyY]: 'KeyY',
  [KeyCode.KeyU]: 'KeyU',
  [KeyCode.KeyI]: 'KeyI',
  [KeyCode.KeyO]: 'KeyO',
  [KeyCode.KeyP]: 'KeyP',
  [KeyCode.LeftBracket]: 'BracketLeft',
  [KeyCode.RightBracket]: 'BracketRight',
  [KeyCode.Return]: 'Enter',
  [KeyCode.ControlLeft]: 'ControlLeft',
  [KeyCode.KeyA]: 'KeyA',
  [KeyCode.KeyS]: 'KeyS',
  [KeyCode.KeyD]: 'KeyD',
  [KeyCode.KeyF]: 'KeyF',
  [KeyCode.KeyG]: 'KeyG',
  [KeyCode.KeyH]: 'KeyH',
  [KeyCode.KeyJ]: 'KeyJ',
  [KeyCode.KeyK]: 'KeyK',
  [KeyCode.KeyL]: 'KeyL',
  [KeyCode.SemiColon]: 'SemiColon',
  [KeyCode.Quote]: 'Quote',
  [KeyCode.BackQuote]: 'BackQuote',
  [KeyCode.ShiftLeft]: 'ShiftLeft',
  [KeyCode.BackSlash]: 'BackSlash',
  [KeyCode.KeyZ]: 'KeyZ',
  [KeyCode.KeyX]: 'KeyX',
  [KeyCode.KeyC]: 'KeyC',
  [KeyCode.KeyV]: 'KeyV',
  [KeyCode.KeyB]: 'KeyB',
  [KeyCode.KeyN]: 'KeyN',
  [KeyCode.KeyM]: 'KeyM',
  [KeyCode.Comma]: 'Comma',
  [KeyCode.Dot]: 'Period',
  [KeyCode.Slash]: 'Slash',
  [KeyCode.ShiftRight]: 'ShiftRight',
  [KeyCode.KpMultiply]: 'NumpadMultiply',
  [KeyCode.Alt]: 'Alt',
  [KeyCode.Space]: 'Space',
  [KeyCode.CapsLock]: 'CapsLock',
  [KeyCode.F1]: 'F1',
  [KeyCode.F2]: 'F2',
  [KeyCode.F3]: 'F3',
  [KeyCode.F4]: 'F4',
  [KeyCode.F5]: 'F5',
  [KeyCode.F6]: 'F6',
  [KeyCode.F7]: 'F7',
  [KeyCode.F8]: 'F8',
  [KeyCode.F9]: 'F9',
  [KeyCode.F10]: 'F10',
  [KeyCode.ScrollLock]: 'ScrollLock',
  [KeyCode.Num7]: 'Numpad7',
  [KeyCode.Num8]: 'Numpad8',
  [KeyCode.Num9]: 'Numpad9',
  [KeyCode.KpMinus]: 'NumpadSubtract',
  [KeyCode.Num4]: 'Numpad4',
  [KeyCode.Num5]: 'Numpad5',
  [KeyCode.Num6]: 'Numpad6',
  [KeyCode.KpPlus]: 'NumpadAdd',
  [KeyCode.Num1]: 'Numpad1',
  [KeyCode.Num2]: 'Numpad2',
  [KeyCode.Num3]: 'Numpad3',
  [KeyCode.Num0]: 'Numpad0',
  // Numpad Decimal?
  [KeyCode.IntlBackslash]: 'IntlBackslash',
  [KeyCode.F11]: 'F11',
  [KeyCode.F12]: 'F12',
  // NumpadEqual?
  [KeyCode.F13]: 'F13',
  [KeyCode.F14]: 'F14',
  [KeyCode.F15]: 'F15',
  [KeyCode.F16]: 'F16',
  [KeyCode.F17]: 'F17',
  [KeyCode.F18]: 'F18',
  [KeyCode.F19]: 'F19',
  [KeyCode.F20]: 'F20',
  [KeyCode.F21]: 'F21',
  [KeyCode.F22]: 'F22',
  [KeyCode.F23]: 'F23',
  [KeyCode.F24]: 'F24',
  // NumpadComma?
  [KeyCode.PreviousTrack]: 'MediaTrackPrevious',
  [KeyCode.NextTrack]: 'MediaTrackNext',
  [KeyCode.KpReturn]: 'NumpadEnter',
  [KeyCode.ControlRight]: 'ControlRight',
  [KeyCode.VolumeMute]: 'VolumeMute',
  [KeyCode.PlayPause]: 'MediaPlayPause',
  [KeyCode.VolumeDown]: 'VolumeDown',
  [KeyCode.VolumeUp]: 'VolumeUp',
  [KeyCode.KpDivide]: 'NumpadDivide',
  [KeyCode.PrintScreen]: 'PrintScreen',
  [KeyCode.AltGr]: 'AltGr',
  [KeyCode.NumLock]: 'NumLock',
  [KeyCode.Pause]: 'Pause',
  [KeyCode.Home]: 'Home',
  [KeyCode.UpArrow]: 'ArrowUp',
  [KeyCode.PageUp]: 'PageUp',
  [KeyCode.LeftArrow]: 'ArrowLeft',
  [KeyCode.RightArrow]: 'ArrowRight',
  [KeyCode.End]: 'End',
  [KeyCode.DownArrow]: 'ArrowDown',
  [KeyCode.PageDown]: 'PageDown',
  [KeyCode.Insert]: 'Insert',
  [KeyCode.Delete]: 'Delete',
  // also known as "windows", "super", and "command"
  [KeyCode.MetaLeft]: 'MetaLeft',
  // also known as "windows", "super", and "command"
  [KeyCode.MetaRight]: 'MetaRight',

  [KeyCode.KpDelete]: 'NumpadDelete',
  [KeyCode.Function]: 'Function',
  [KeyCode.BrightnessUp]: 'BrightnessUp',
  [KeyCode.BrightnessDown]: 'BrightnessDown',
  [KeyCode.PlayCd]: 'PlayCd',
  [KeyCode.Unknown]: 'Unknown',
}

export const mapKeyKey: Record<KeyCode, string> = {
  [KeyCode.Escape]: 'Escape',
  [KeyCode.Kp0]: '0',
  [KeyCode.Kp1]: '1',
  [KeyCode.Kp2]: '2',
  [KeyCode.Kp3]: '3',
  [KeyCode.Kp4]: '4',
  [KeyCode.Kp5]: '5',
  [KeyCode.Kp6]: '6',
  [KeyCode.Kp7]: '7',
  [KeyCode.Kp8]: '8',
  [KeyCode.Kp9]: '9',
  [KeyCode.Minus]: '-',
  [KeyCode.Equal]: '=',
  [KeyCode.Backspace]: 'Backspace',
  [KeyCode.Tab]: 'Tab',
  [KeyCode.KeyQ]: 'q',
  [KeyCode.KeyW]: 'w',
  [KeyCode.KeyE]: 'e',
  [KeyCode.KeyR]: 'r',
  [KeyCode.KeyT]: 't',
  [KeyCode.KeyY]: 'y',
  [KeyCode.KeyU]: 'u',
  [KeyCode.KeyI]: 'i',
  [KeyCode.KeyO]: 'o',
  [KeyCode.KeyP]: 'p',
  [KeyCode.LeftBracket]: '[',
  [KeyCode.RightBracket]: ']',
  [KeyCode.Return]: 'Enter',
  [KeyCode.ControlLeft]: 'Control',
  [KeyCode.KeyA]: 'a',
  [KeyCode.KeyS]: 's',
  [KeyCode.KeyD]: 'd',
  [KeyCode.KeyF]: 'f',
  [KeyCode.KeyG]: 'g',
  [KeyCode.KeyH]: 'h',
  [KeyCode.KeyJ]: 'j',
  [KeyCode.KeyK]: 'k',
  [KeyCode.KeyL]: 'l',
  [KeyCode.SemiColon]: ';',
  [KeyCode.Quote]: '\'',
  [KeyCode.BackQuote]: '`',
  [KeyCode.ShiftLeft]: 'Shift',
  [KeyCode.BackSlash]: '\\',
  [KeyCode.KeyZ]: 'z',
  [KeyCode.KeyX]: 'x',
  [KeyCode.KeyC]: 'c',
  [KeyCode.KeyV]: 'v',
  [KeyCode.KeyB]: 'b',
  [KeyCode.KeyN]: 'n',
  [KeyCode.KeyM]: 'm',
  [KeyCode.Comma]: ',',
  [KeyCode.Dot]: '.',
  [KeyCode.Slash]: '/',
  [KeyCode.ShiftRight]: 'Shift',
  [KeyCode.KpMultiply]: '*',
  [KeyCode.Alt]: 'Alt',
  [KeyCode.Space]: ' ',
  [KeyCode.CapsLock]: 'CapsLock',
  [KeyCode.F1]: 'F1',
  [KeyCode.F2]: 'F2',
  [KeyCode.F3]: 'F3',
  [KeyCode.F4]: 'F4',
  [KeyCode.F5]: 'F5',
  [KeyCode.F6]: 'F6',
  [KeyCode.F7]: 'F7',
  [KeyCode.F8]: 'F8',
  [KeyCode.F9]: 'F9',
  [KeyCode.F10]: 'F10',
  [KeyCode.ScrollLock]: 'ScrollLock',
  [KeyCode.Num7]: '7',
  [KeyCode.Num8]: '8',
  [KeyCode.Num9]: '9',
  [KeyCode.KpMinus]: '-',
  [KeyCode.Num4]: '4',
  [KeyCode.Num5]: '5',
  [KeyCode.Num6]: '6',
  [KeyCode.KpPlus]: '+',
  [KeyCode.Num1]: '1',
  [KeyCode.Num2]: '2',
  [KeyCode.Num3]: '3',
  [KeyCode.Num0]: '0',
  // Numpad Decimal?
  [KeyCode.IntlBackslash]: 'IntlBackslash',
  [KeyCode.F11]: 'F11',
  [KeyCode.F12]: 'F12',
  // NumpadEqual?
  [KeyCode.F13]: 'F13',
  [KeyCode.F14]: 'F14',
  [KeyCode.F15]: 'F15',
  [KeyCode.F16]: 'F16',
  [KeyCode.F17]: 'F17',
  [KeyCode.F18]: 'F18',
  [KeyCode.F19]: 'F19',
  [KeyCode.F20]: 'F20',
  [KeyCode.F21]: 'F21',
  [KeyCode.F22]: 'F22',
  [KeyCode.F23]: 'F23',
  [KeyCode.F24]: 'F24',
  // NumpadComma?
  [KeyCode.PreviousTrack]: 'MediaTrackPrevious',
  [KeyCode.NextTrack]: 'MediaTrackNext',
  [KeyCode.KpReturn]: 'Enter',
  [KeyCode.ControlRight]: 'Control',
  [KeyCode.VolumeMute]: 'VolumeMute',
  [KeyCode.PlayPause]: 'MediaPlayPause',
  [KeyCode.VolumeDown]: 'VolumeDown',
  [KeyCode.VolumeUp]: 'VolumeUp',
  [KeyCode.KpDivide]: '/',
  [KeyCode.PrintScreen]: 'PrintScreen',
  [KeyCode.AltGr]: 'Alt',
  [KeyCode.NumLock]: 'NumLock',
  [KeyCode.Pause]: 'Pause',
  [KeyCode.Home]: 'Home',
  [KeyCode.UpArrow]: 'ArrowUp',
  [KeyCode.PageUp]: 'PageUp',
  [KeyCode.LeftArrow]: 'ArrowLeft',
  [KeyCode.RightArrow]: 'ArrowRight',
  [KeyCode.End]: 'End',
  [KeyCode.DownArrow]: 'ArrowDown',
  [KeyCode.PageDown]: 'PageDown',
  [KeyCode.Insert]: 'Insert',
  [KeyCode.Delete]: 'Delete',
  // also known as "windows", "super", and "command"
  [KeyCode.MetaLeft]: 'Meta',
  // also known as "windows", "super", and "command"
  [KeyCode.MetaRight]: 'Meta',

  [KeyCode.KpDelete]: 'Delete',
  [KeyCode.Function]: 'Function',
  [KeyCode.BrightnessUp]: 'BrightnessUp',
  [KeyCode.BrightnessDown]: 'BrightnessDown',
  [KeyCode.PlayCd]: 'PlayCd',
  [KeyCode.Unknown]: 'Unknown',
}
