---
title: 有关 Neuro-sama
---

热议与见解：

- [r/VirtualYoutubers --- Someone help me understand Neuro-sama : r/VirtualYoutubers](https://www.reddit.com/r/VirtualYoutubers/comments/1gi5ra0/someone_help_me_understand_neurosama/)
- [How to Make an AI Vtuber. - YouTube](https://www.youtube.com/watch?v=WZ9JqlxQ6iQ)
- [How neuro plays Minecraft? : r/NeuroSama](https://www.reddit.com/r/NeuroSama/comments/1hi8seg/how_neuro_plays_minecraft/)
- [How to make an AI VTuber Using GPT 3 and Google Cloud TTS - YouTube](https://www.youtube.com/watch?v=EXICATDyYWI)
- [Having a personal neuro-sama? : r/NeuroSama](https://www.reddit.com/r/NeuroSama/comments/1ix5uip/having_a_personal_neurosama/)
- [Is it really Neuro-sama playing Minecraft? : r/NeuroSama](https://www.reddit.com/r/NeuroSama/comments/1ifhv0f/is_it_really_neurosama_playing_minecraft/)
- [Is neuro custom coded from the ground up or does she have a base model? : r/NeuroSama](https://www.reddit.com/r/NeuroSama/comments/19481ow/is_neuro_custom_coded_from_the_ground_up_or_does/)

我们可以使用此插件让 AI / LLM 控制模型：

- [pladisdev/VTS-AI-Plugin](https://github.com/pladisdev/VTS-AI-Plugin)

- [Plugins · DenchiSoft/VTubeStudio Wiki](https://github.com/DenchiSoft/VTubeStudio/wiki/Plugins)

一些现有的开源项目：

- [JarodMica/open-neruosama](https://github.com/JarodMica/open-neruosama/tree/master)
- [AIVTDevPKevin/AI-VTuber-System: A graphical system program that allows you to quickly create your own AI VTuber for free.](https://github.com/AIVTDevPKevin/AI-VTuber-System)
