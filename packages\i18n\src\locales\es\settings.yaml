animations:
  stage-transitions:
    title: Desactivar Transiciones del Escenario
  use-page-specific-transitions:
    description: >
      Algunas páginas tendrán sus propias transiciones, esto anulará las transiciones
      del escenario
    title: Usar Transiciones Específicas de Página
dialogs:
  onboarding:
    title: ¡Bienvenido a AIRI!
    description: Configuremos tu primer proveedor de IA para comenzar.
    selectProvider: Elige un Proveedor de IA
    configureProvider: Configurar {provider}
    apiKey: Clave API
    apiKeyHelp: Obtén tu clave API de {provider} y pégala aquí
    baseUrl: URL Base
    baseUrlHelp: URL del endpoint de la API (usa el predeterminado si no estás seguro)
    accountId: ID de Cuenta
    validationSuccess: La validación de la configuración fue exitosa
    validationFailed: La validación de la configuración falló
    validationError: 'Error de validación: {error}'
    skipForNow: Omitir por ahora
    saveAndContinue: Guardar y Continuar
    next: Siguiente
    start: ¡Hagámoslo!
    select-model: Elegir modelo
language:
  title: Idioma
  description: >
    Cambia el idioma de la interfaz de AIRI. Esto no afectará el idioma
    de las respuestas del personaje.
live2d:
  change-model:
    from-file: Cargar desde Archivo
    from-file-select: Seleccionar
    from-url: Cargar desde URL
    from-url-confirm: Cargar
    from-url-placeholder: Ingresa la URL del modelo Live2D
    title: Cambiar Modelo
  edit-motion-map:
    title: Editar mapa de movimientos
  map-motions:
    play: Reproducir Movimiento
    title: Mapear Movimientos
  title: Configuración Live2D
  scale-and-position:
    title: Escala y Posición
    scale: Escala
    x: X
    'y': 'Y'
  switch-to-vrm:
    title: ¿Cambiar a Avatar 3D?
    change-to-vrm: Haz clic aquí para cambiar a la configuración de avatar 3D (VRM)
  theme-color-from-model:
    title: Extraer colores del modelo
    button-extract:
      title: Extraer
  focus:
    title: Desactivar seguimiento del ratón del modelo
    button-disable:
      title: Desactivar
microphone: Micrófono
models: Modelo
pages:
  card:
    activate: Activar
    active: Activo
    active_badge: Actualmente Activo
    cancel: Cancelar
    card_not_found: Tarjeta no encontrada
    character: Personaje
    close: Cerrar
    consciousness:
      model: Consciencia / Modelo
    created_by: creado por
    creator_notes: Notas del Creador
    delete: Eliminar
    delete_card: Eliminar Tarjeta
    delete_confirmation: ¿Estás seguro de que quieres eliminar esta tarjeta?
    description: Usar presets de tarjeta de personaje AIRI
    description_label: Descripción
    drop_here: Suelta para subir
    create_card: Crear una nueva Tarjeta
    creation:
      identity: Identidad
      name: Nombre
      nickname: Apodo
      description: Descripción
      behavior: Comportamiento
      greetings: Saludos (uno por línea)
      settings: Configuración
      version: Versión
      create: Crear
      defaults:
        name: Nombre
        personality: Eres un humano normal, curioso sobre todo.
        scenario: Recientemente despertaste y olvidaste todo sobre tu vida anterior.
        systemprompt: Recibirás mensajes, respóndelos como un humano real.
        posthistoryinstructions: Recuerda imitar a un humano.
      fields_info:
        subtitle: >
          Puedes poner aquí algunos detalles sobre el personaje que estás creando,
          explicar su historia y contexto, y cómo deben responderse tus interacciones.
        name: Es el nombre formal de este personaje.
        nickname: También puedes dar un apodo que se usará con prioridad.
        description: Descripción de este personaje.
        notes: Si quieres agregar algunas notas personales.
        personality: >
          Describe aquí la personalidad de tu personaje. ¿Tímido? ¿Curioso?
          ¿Algo más?
        scenario: ¿Cuáles son los alrededores? ¿Cuál es la situación actual?
        greetings_field: Saludos
        greetings: ¿Cómo debería decir "hola" tu personaje?
        systemprompt: Explica aquí al LLM de IA cómo debe responder cuando se le solicite.
        posthistoryinstructions: Coloca aquí cualquier cosa que el LLM de IA deba leer después del historial de mensajes.
        version: >
          Versión de la tarjeta, deberías aumentar esto si estás haciendo cambios desde
          una tarjeta anterior.
      errors:
        name: El nombre debe ser válido o no estar vacío.
        version: '¡Error: Número de versión inválido!'
        description: 'Error: Debes proporcionar una descripción para esta tarjeta.'
        personality: 'Error: Se debe proporcionar una personalidad para este personaje.'
        scenario: 'Error: Se requiere un escenario.'
        systemprompt: 'Error: Por favor, proporciona un prompt del sistema.'
        posthistoryinstructions: 'Error: Se requiere un prompt post-historial.'
    modules: Módulos
    name_asc: Nombre (A-Z)
    name_desc: Nombre (Z-A)
    no_cards: Aún no hay tarjetas. ¡Haz clic en el botón de arriba para subir una!
    no_results: No se encontraron tarjetas coincidentes
    personality: Personalidad
    posthistoryinstructions: Instrucciones Post-Historial
    recent: Agregadas Recientemente
    scenario: Escenario
    search: Buscar tarjetas...
    sort_by: Ordenar por
    speech:
      model: Habla / Modelo
      voice: Habla / Voz
    systemprompt: Prompt del Sistema
    title: Tarjeta AIRI
    try_different_search: Prueba un término de búsqueda diferente
    upload: Subir
    upload_desc: Haz clic o arrastra el archivo para subir
  memory:
    description: Donde se almacenan y organizan las memorias
    title: Memoria
  models:
    description: Live2D, VRM, etc.
    title: Modelos
    sections:
      section:
        live2d:
          title: Live2D
          description: Configurar modelos Live2D y configuraciones
        vrm:
          title: VRM
          description: Configurar modelos VRM 3D y configuraciones
        scene: Escena
  modules:
    consciousness:
      description: Personalidad, modelo deseado, etc.
      sections:
        section:
          provider-model-selection:
            collapse: Colapsar
            custom_model_placeholder: Ingresa el nombre del modelo personalizado...
            description: Selecciona el proveedor LLM adecuado para la consciencia
            error: Error cargando modelos
            expand: Expandir
            loading: Cargando modelos disponibles...
            manual_model_name: Nombre del Modelo
            manual_model_placeholder: Ingresa el nombre del modelo para usar con este proveedor
            no_models: No hay modelos disponibles
            no_models_description: No se encontraron modelos para este proveedor
            no_search_results: No hay modelos coincidentes
            no_search_results_description: Ningún modelo coincide con '{query}'. Prueba un término de búsqueda diferente.
            not_supported: Listado de modelos no soportado
            not_supported_description: Este proveedor no soporta la funcionalidad de listado de modelos
            search_placeholder: Buscar modelos...
            search_results: Encontrados {count} de {total} modelos
            show_less: Mostrar menos
            show_more: Mostrar más
            subtitle: Selecciona un modelo del proveedor
            title: Modelo
      title: Consciencia
    description: Pensamiento, visión, síntesis de voz, juegos, etc.
    gaming-factorio:
      description: ¡Jugando Factorio!
      title: Factorio
    gaming-minecraft:
      description: ¡Jugando Minecraft!
      title: Minecraft
    hearing:
      description: Configurar cómo funciona el reconocimiento de voz
      title: Audición
    memory-long-term:
      description: Configuraciones específicas y gestión de memoria a largo plazo
      title: Memoria a Largo Plazo
    memory-short-term:
      description: Configuraciones específicas y gestión de memoria a corto plazo
      title: Memoria a Corto Plazo
    messaging-discord:
      description: Chat y chat de voz por Discord
      title: Discord
    speech:
      description: Síntesis de voz
      sections:
        section:
          playground:
            buttons:
              stop:
                label: Detener
            select-voice:
              required: Por favor selecciona una voz
          provider-voice-selection:
            custom_model_placeholder: Ingresa el nombre del modelo personalizado...
            custom_voice_placeholder: Ingresa el ID de voz personalizado...
            description: Selecciona el proveedor de voz adecuado
            no_models: No hay modelos disponibles
            no_models_description: No se encontraron modelos para este proveedor
            no_voices: No hay voces disponibles
            no_voices_description: No se encontraron voces para este proveedor
            pause: Pausar
            play_sample: Reproducir Muestra
            search_models_placeholder: Buscar modelos...
            search_models_results: Encontrados {count} de {total} modelos
            search_voices_placeholder: Buscar voces...
            search_voices_results: Encontradas {count} de {total} voces
            show_less: Mostrar menos
            show_more: Mostrar más
            title: Proveedor
          voice-settings:
            input-ssml:
              placeholder: Ingresa texto SSML...
            use-ssml:
              description: Habilitar para ingresar SSML crudo en lugar de texto plano
              label: Usar SSML Personalizado
      title: Habla
    title: Módulos
    vision:
      description: Visión
      title: Visión
    x:
      description: Navegación y uso de X / Twitter
      title: X / Twitter
    mcp-server:
      description: Conectar y gestionar servidor MCP y herramientas
      title: Servidor MCP
  providers:
    explained:
      chat: Proveedores de modelos de generación de texto. ej. OpenRouter, OpenAI, Ollama.
      Speech: Proveedores de modelos de habla (texto a voz). ej. ElevenLabs, Azure Speech.
      Transcription: >
        Proveedores de modelos de transcripción (voz a texto). ej. Whisper.cpp,
        OpenAI, Azure Speech
    helpinfo:
      title: ¿Primera vez aquí?
      description: >
        AIRI requiere que al menos un proveedor de {chat} esté configurado para pensar
        y comportarse correctamente. Podrías pensarlo como el cerebro de los
        personajes que viven en el sistema AIRI.
    common:
      fields:
        field:
          api-key:
            label: Entrada de Clave API
      section:
        advanced:
          fields:
            field:
              headers:
                description: Agregar encabezados HTTP personalizados
                key:
                  placeholder: Clave
                label: Encabezados HTTP
                value:
                  placeholder: Valor
          title: Avanzado
        basic:
          description: Configuraciones esenciales
          title: Básico
        voice:
          title: Configuraciones de Voz
    description: LLMs, proveedores de voz, etc.
    provider:
      app-local-audio-transcription:
        title: App (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      app-local-audio-speech:
        title: App (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      browser-local-audio-transcription:
        title: Navegador (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      browser-local-audio-speech:
        title: Navegador (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      alibaba-cloud-model-studio:
        description: bailian.console.aliyun.com
        title: Alibaba Cloud Model Studio
      anthropic:
        description: anthropic.com
        title: Anthropic | Claude
        helpinfo:
          title: Antes de comenzar
          description:
            part1: >
              Aunque Anthropic recientemente anunció que tienen soporte beta
              para compatibilidad con SDK de OpenAI
            part2: (puedes leer más aquí)
            part3: pero debido a los detalles de implementación que vienen con
            part4: >
              restricciones que no están alineadas con el SDK de OpenAI, actualmente
              no es posible usar este proveedor en el navegador.
            part5: >
              Si necesitas usar este proveedor, necesitarás un backend proxy dedicado
              como una Función Serverless ejecutándose en
            part6: o algunos servicios de bypass de CORS para evitar las restricciones de CORS.
      cloudflare-workers-ai:
        description: cloudflare.com
        fields:
          field:
            account-id:
              description: ID de Cuenta de Cloudflare
              label: ID de Cuenta
              placeholder: Tu ID de Cuenta de Cloudflare
            api-key:
              placeholder: Ingresa la Clave API de Cloudflare
        title: Cloudflare Workers AI
      common:
        fields:
          field:
            pitch:
              description: Ajustar el tono del habla sintetizada (ej., más agudo o más grave)
              label: Tono
            speed:
              description: Ajustar la velocidad del habla
              label: Velocidad
            volume:
              description: Ajustar el volumen del habla
              label: Volumen
      deepseek:
        description: deepseek.com
        title: DeepSeek
      elevenlabs:
        description: elevenlabs.io
        fields:
          field:
            simularity-boost:
              description: Adherencia a la similitud de voz
              label: Impulso de Similitud
            speaker-boost:
              description: Mejorar la similitud del hablante
              label: Impulso del Hablante
            speed:
              description: Velocidad de generación de voz
              label: Velocidad
            stability:
              description: Estabilidad de voz y aleatoriedad
              label: Estabilidad
            style:
              description: Exageración del estilo de voz
              label: Estilo
        playground:
          buttons:
            button:
              test-voice:
                generating: Generando...
                label: Probar Voz
          fields:
            field:
              input:
                placeholder: Ingresa texto para probar la voz...
              language:
                description: Seleccionar idioma de voz
                label: Idioma
              voice:
                description: Seleccionar voz preferida
                label: Voz
          title: Playground de Voz
          validation:
            error-missing-api-key: Por favor ingresa una clave API para probar la voz.
        title: ElevenLabs
      fireworks:
        description: fireworks.ai
        title: Fireworks.ai
      microsoft-speech:
        description: speech.microsoft.com
        fields:
          field:
            region:
              description: Región del Servicio de Voz
              label: Región
        title: Microsoft / Azure Speech
      index-tts-vllm:
        description: https://index-tts.github.io/
        title: Bilibili / IndexTTS
      azure-ai-foundry:
        description: Azure AI Foundry
        title: Azure AI Foundry
      mistral:
        description: mistral.ai
        title: Mistral
      moonshot:
        description: moonshot.ai
        title: Moonshot AI
      modelscope:
        description: modelscope.cn
        title: ModelScope
      novita:
        description: novita.ai
        title: Novita
      ollama:
        description: ollama.ai
        title: Ollama
      openai:
        description: openai.com
        title: OpenAI
      openai-compatible:
        description: OpenAI Compatible
        title: OpenAI Compatible
      openrouter:
        description: openrouter.ai
        title: OpenRouter
      perplexity:
        description: perplexity.ai
        title: Perplexity
      player2:
        description: player2.game
        title: Player2
      together:
        description: together.ai
        title: Together.ai
      google-generative-ai:
        description: gemini.google.com
        title: Google Gemini
      featherless:
        description: featherless.ai
        title: Featherless AI
      lm-studio:
        description: lmstudio.ai
        title: LM Studio
      vllm:
        description: vllm.ai
        title: vLLM
      volcengine:
        description: volcengine.com
        fields:
          field:
            appId:
              description: ID de App del proyecto que puedes obtener en la Consola
              label: ID de App
        title: Volcano Engine
      xai:
        description: x.ai
        title: xAI
      transcriptions:
        playground:
          title: Playground de Transcripción
    title: Proveedores
  scene:
    description: Configurar el entorno donde vive el personaje
    title: Escena
  themes:
    color-scheme:
      description: Cambiar el esquema de colores del escenario.
      title: Esquema de Colores
    developer:
      description: Algunas opciones de desarrollador.
      title: Desarrolladores
    general:
      description: Tema oscuro, idiomas, etc.
      title: General
    description: ¡Personaliza tu escenario!
    sections:
      section:
        custom-color:
          fields:
            field:
              primary-color:
                label: Color primario
                rgb-on:
                  title: ¡Lo Quiero Dinámico!
          title: Esquema de Colores
        developer:
          title: Desarrollador
        theme-presets:
          presets:
            - colors:
                - Verde AIRI
              description: ¡El color de tema verdoso predeterminado, traído por AIRI para ti!
              title: Color Predeterminado
            - colors:
                - Taupe
                - Beige
                - Gris Ceniza
                - Taupe Claro
                - Marfil
                - Gris Oliva
                - Arena
                - Gris Cálido
              description: Tonos suaves y apagados inspirados en las pinturas de Giorgio Morandi
              title: Colores Morandi
            - colors:
                - Azul Cielo
                - Niebla
                - Arena
                - Verde Musgo
                - Nenúfar
                - Trigo
                - Azul Pizarra
                - Salvia
              description: Paleta impresionista inspirada en las obras de Claude Monet
              title: Colores Monet
            - colors:
                - Bronceado
                - Taupe Cálido
                - Umber
                - Café
                - Bronce
                - Oro
                - Mostaza
                - Ámbar
              description: Paleta de colores japonesa tradicional
              title: Colores Japoneses
            - colors:
                - Azul Nórdico
                - Hielo
                - Fiordo
                - Acero
                - Glaciar
                - Pizarra
                - Nube
                - Piedra
              description: Esquema de colores minimalista escandinavo
              title: Colores Nórdicos
            - colors:
                - Amanecer Rosado
                - Rojo Chino
                - Marrón Ahumado
                - Verde Bambú
                - Púrpura Oscuro
                - Amarillo Dorado
                - Azul Azure
                - Ocre
              description: >
                Colores chinos tradicionales, derivados de textiles antiguos,
                porcelana y pinturas
              title: Colores Tradicionales Chinos
          title: Presets de Esquema de Colores
    title: Apariencia
sections:
  section:
    general:
      title: General
theme:
  title: Tema
  description: |
    Cambiar el tema base de AIRI, modo Claro o modo Oscuro.
title: Configuración
voices: Voz
vrm:
  change-model:
    from-file: Cargar desde Archivo
    from-file-select: Seleccionar
    from-url: Cargar desde URL
    from-url-confirm: Cargar
    from-url-placeholder: Ingresa la URL del modelo VRM
    title: Cambiar Modelo
  title: Configuración VRM
  scale-and-position:
    model-info-title: Información del Tamaño del Modelo
    model-info-x: Ancho (X)
    model-info-y: Alto (Y)
    model-info-z: Profundidad (Z)
    tips: |
      Edita la posición inicial del modelo VRM.
      Los ejes de coordenadas están visualizados.
    scale: Escala
    x: Desplazamiento X
    'y': Desplazamiento Y
    z: Desplazamiento Z
    fov: FOV (grados)
    rotation-y: Rotación (eje Y)
    camera-distance: Distancia de cámara
    eye-tracking-mode:
      title: Mirando a
      options:
        option:
          camera: Cámara
          mouse: Ratón
          disabled: Desactivado
  switch-to-vrm:
    title: ¿Cambiar a Avatar Live2D?
    change-to-vrm: Haz clic aquí para cambiar a la configuración de avatar Live2D
  theme-color-from-model:
    button-extract:
      title: Extraer
