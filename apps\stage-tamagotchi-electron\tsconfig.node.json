{"extends": "../../tsconfig.json", "compilerOptions": {"composite": true, "target": "ESNext", "jsx": "preserve", "lib": ["ESNext"], "useDefineForClassFields": true, "resolveJsonModule": true, "types": ["vitest", "vite/client", "electron-vite/node"], "allowJs": true, "strict": true, "skipLibCheck": true}, "include": ["vite.config.ts", "electron.vite.config.ts", "src/main/**/*.ts", "src/main/**/*.d.ts", "src/main/**/*.tsx", "src/main/**/*.vue", "src/preload/**/*.ts", "src/preload/**/*.d.ts", "src/preload/**/*.tsx", "src/preload/**/*.vue", "src/shared/**/*.ts", "src/shared/**/*.d.ts", "src/shared/**/*.tsx", "src/shared/**/*.vue"]}