animations:
  stage-transitions:
    title: Disable Stage Transitions
  use-page-specific-transitions:
    description: >-
      Some pages will have their own transitions, this will override the stage
      transitions
    title: Use Page Specific Transitions
dialogs:
  onboarding:
    title: Welcome to AIRI!
    description: Let's set up your first AI provider to get started.
    selectProvider: Choose an AI Provider
    configureProvider: Configure {provider}
    apiKey: API Key
    apiKeyHelp: Get your API key from {provider} and paste it here
    baseUrl: Base URL
    baseUrlHelp: API endpoint URL (use default if unsure)
    accountId: Account ID
    validationSuccess: Configuration validation success
    validationFailed: Configuration validation failed
    validationError: 'Validation error: {error}'
    skipForNow: Skip for now
    saveAndContinue: Save and Continue
    next: Next
    start: Let's do it!
    select-model: Choose model
    no-models: No available models
    no-models-help: >-
      Please return to the previous step and check your API key, or check the
      network connection.
language:
  title: Language
  description: >
    Change the language of the AIRI interface. This will not affect the language
    of the character's responses.
live2d:
  change-model:
    from-file: Load from File
    from-file-select: Select
    from-url: Load from URL
    from-url-confirm: Load
    from-url-placeholder: Enter Live2D model URL
    title: Change Model
  edit-motion-map:
    title: Edit motion map
  map-motions:
    play: Play Motion
    title: Map Motions
  title: Live2D Settings
  scale-and-position:
    title: Scale And Position
    scale: Scale
    x: X
    'y': 'Y'
  switch-to-vrm:
    title: Switch to 3D Avatar?
    change-to-vrm: Click here to switch to the 3D avatar setting (VRM)
  theme-color-from-model:
    title: Extract colors from model
    button-extract:
      title: Extract
  focus:
    title: Disable model mouse tracking
    button-disable:
      title: Disable
microphone: Microphone
models: Model
pages:
  card:
    activate: Activate
    active: Active
    active_badge: Currently Active
    cancel: Cancel
    card_not_found: Card not found
    character: Character
    close: Close
    consciousness:
      model: Consciousness / Model
    created_by: created by
    creator_notes: Creator Notes
    delete: Delete
    delete_card: Delete Card
    delete_confirmation: Are you sure you want to delete this card?
    description: Use AIRI character card presets
    description_label: Description
    drop_here: Drop to upload
    create_card: Create a new Card
    creation:
      identity: Identity
      name: Name
      nickname: Nickname
      description: Description
      behavior: Behavior
      greetings: Greetings (one per line)
      settings: Settings
      version: Version
      create: Create
      defaults:
        name: Name
        personality: You are a regular human, curious about everything.
        scenario: You recently woke up and forgot everything about your previous life.
        systemprompt: You will receive messages, answer to them like a real human.
        posthistoryinstructions: Remember to imitate an human.
      fields_info:
        subtitle: >-
          You can put here some details about the character you are creating,
          explain his history and context, and how your interactions should be
          answered.
        name: Is the formal name of this character.
        nickname: You can also give a nickname that will be used in priority.
        description: Description of this character.
        notes: If you want to add some personal notes.
        personality: >-
          Describe here the personality of your character. Shy ? Curious ?
          Anything else ?
        scenario: What are the surroundings ? What is the current situation ?
        greetings_field: Greetings
        greetings: How your character should say "hello" ?
        systemprompt: Explain here to the AI LLM how it should answer when prompted.
        posthistoryinstructions: Place here anything the AI LLM should read after the messages history.
        version: >-
          Card version, you should increase this if you are making changes from
          a previous card.
      errors:
        name: Name should be valid or non-empty.
        version: 'Error: Invalid version number !'
        description: 'Error: You must provide a description for this card.'
        personality: 'Error: A personality must be provided for this character.'
        scenario: 'Error: A scenario is required.'
        systemprompt: 'Error: Please, provide a system prompt.'
        posthistoryinstructions: 'Error: Post history prompt is required.'
    modules: Modules
    name_asc: Name (A-Z)
    name_desc: Name (Z-A)
    no_cards: No cards yet. Click the button above to upload one!
    no_results: No matching cards found
    personality: Personality
    posthistoryinstructions: Post-History Instructions
    recent: Recently Added
    scenario: Scenario
    search: Search cards...
    sort_by: Sort by
    speech:
      model: Speech / Model
      voice: Speech / Voice
    systemprompt: System Prompt
    title: AIRI Card
    try_different_search: Try a different search term
    upload: Upload
    upload_desc: Click or drag file to upload
  memory:
    description: Where memories got stored, and organized
    title: Memory
  models:
    description: Live2D, VRM, etc.
    title: Models
    sections:
      section:
        live2d:
          title: Live2D
          description: Configure Live2D models and settings
        vrm:
          title: VRM
          description: Configure 3D VRM models and settings
        scene: Scene
  modules:
    consciousness:
      description: Personality, desired model, etc.
      sections:
        section:
          provider-model-selection:
            collapse: Collapse
            custom_model_placeholder: Enter custom model name...
            description: Select the suitable LLM provider for consciousness
            error: Error loading models
            expand: Expand
            loading: Loading available models...
            manual_model_name: Model Name
            manual_model_placeholder: Enter the model name to use with this provider
            no_models: No models available
            no_models_description: No models were found for this provider
            no_search_results: No matching models
            no_search_results_description: No models match '{query}'. Try a different search term.
            not_supported: Model listing not supported
            not_supported_description: This provider doesn't support model listing functionality
            search_placeholder: Search models...
            search_results: Found {count} of {total} models
            show_less: Show less
            show_more: Show more
            subtitle: Select a model from the provider
            title: Model
      title: Consciousness
    description: Thinking, vision, speech synthesis, gaming, etc.
    gaming-factorio:
      description: Playing Factorio!
      title: Factorio
    gaming-minecraft:
      description: Playing Minecraft!
      title: Minecraft
    hearing:
      description: Configure how speech recognition works
      title: Hearing
      sections:
        section:
          provider-selection:
            description: Select the suitable speech recognition provider
    memory-long-term:
      description: Long-term memory specific settings and management
      title: Long-Term Memory
    memory-short-term:
      description: Short-term memory specific settings and management
      title: Short-Term Memory
    messaging-discord:
      description: Chat & voice chat over Discord
      title: Discord
    speech:
      description: Speech synthesis
      sections:
        section:
          playground:
            buttons:
              stop:
                label: Stop
            select-voice:
              required: Please select a voice
          provider-voice-selection:
            custom_model_placeholder: Enter custom model name...
            custom_voice_placeholder: Enter custom voice ID...
            description: Select the suitable speech provider
            no_models: No models available
            no_models_description: No models were found for this provider
            no_voices: No voices available
            no_voices_description: No voices were found for this provider
            no_voices_hint: You can enter a custom voice name below.
            pause: Pause
            play_sample: Play Sample
            search_models_placeholder: Search models...
            search_models_results: Found {count} of {total} models
            search_voices_placeholder: Search voices...
            search_voices_results: Found {count} of {total} voices
            show_less: Show less
            show_more: Show more
            title: Provider
          voice-settings:
            input-ssml:
              placeholder: Enter SSML text...
            use-ssml:
              description: Enable to input raw SSML instead of plain text
              label: Use Custom SSML
      title: Speech
    title: Modules
    vision:
      description: Vision
      title: Vision
    x:
      description: X / Twitter browsing and usage
      title: X / Twitter
    mcp-server:
      description: Connect and manage MCP server and tools
      title: MCP Server
  providers:
    explained:
      chat: Text generation model providers. e.g. OpenRouter, OpenAI, Ollama.
      Speech: Speech (text-to-speech) model providers. e.g. ElevenLabs, Azure Speech.
      Transcription: >-
        Transcription (speech-to-text) model providers. e.g. Whisper.cpp,
        OpenAI, Azure Speech
    helpinfo:
      title: First time here?
      description: >
        AIRI requires at least one {chat} provider to be configured to think,
        and behave properly. You could think of it as the brain of the
        characters living in AIRI system.
    common:
      fields:
        field:
          api-key:
            label: API Key Input
      section:
        advanced:
          fields:
            field:
              headers:
                description: Add custom HTTP headers
                key:
                  placeholder: Key
                label: HTTP Headers
                value:
                  placeholder: Value
          title: Advanced
        basic:
          description: Essential settings
          title: Basic
        voice:
          title: Voice Settings
    description: LLMs, speech providers, etc.
    provider:
      app-local-audio-transcription:
        title: App (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      app-local-audio-speech:
        title: App (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      browser-local-audio-transcription:
        title: Browser (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      browser-local-audio-speech:
        title: Browser (Local)
        description: https://github.com/moeru-ai/xsai-transformers
      alibaba-cloud-model-studio:
        description: bailian.console.aliyun.com
        title: Alibaba Cloud Model Studio
      anthropic:
        description: anthropic.com
        title: Anthropic | Claude
        helpinfo:
          title: Before you start
          description:
            part1: >-
              While Anthropic recently did announce that they are having a beta
              support for OpenAI SDK compatibility
            part2: (you can read more here)
            part3: but due to the implementation details comes with
            part4: >-
              restrictions which not aligned with the OpenAI SDK, it's currently
              not possible to use this provider in the browser.
            part5: >-
              If you do need to use this provider, you will need a dedicated
              proxy backend like a Serverless Function running on
            part6: or some CORS bypassing services to bypass the CORS restrictions.
      cloudflare-workers-ai:
        description: cloudflare.com
        fields:
          field:
            account-id:
              description: Cloudflare Account ID
              label: Account ID
              placeholder: Your Cloudflare Account ID
            api-key:
              placeholder: Input Cloudflare API Key
        title: Cloudflare Workers AI
      common:
        status:
          validating: Validating
          valid: Configuration seems to be valid.
        fields:
          field:
            pitch:
              description: Tune the pitch of synthesized speech (e.g., sharper or coarser)
              label: Pitch
            speed:
              description: Adjust the speed of speech
              label: Speed
            volume:
              description: Adjust the volume of speech
              label: Volume
      deepseek:
        description: deepseek.com
        title: DeepSeek
      elevenlabs:
        description: elevenlabs.io
        fields:
          field:
            simularity-boost:
              description: Voice similarity adherence
              label: Similarity Boost
            speaker-boost:
              description: Enhance speaker similarity
              label: Speaker Boost
            speed:
              description: Speech generation speed
              label: Speed
            stability:
              description: Voice stability and randomness
              label: Stability
            style:
              description: Voice style exaggeration
              label: Style
        playground:
          buttons:
            button:
              test-voice:
                generating: Generating...
                label: Test Voice
          fields:
            field:
              input:
                placeholder: Enter text to test the voice...
              language:
                description: Select voice language
                label: Language
              voice:
                description: Select preferred voice
                label: Voice
          title: Voice Playground
          validation:
            error-missing-api-key: Please enter an API key to test the voice.
        title: ElevenLabs
      fireworks:
        description: fireworks.ai
        title: Fireworks.ai
      microsoft-speech:
        description: speech.microsoft.com
        fields:
          field:
            region:
              description: Speech Service region
              label: Region
        title: Microsoft / Azure Speech
      index-tts-vllm:
        description: https://index-tts.github.io/
        title: Bilibili / IndexTTS
      azure-ai-foundry:
        description: Azure AI Foundry
        title: Azure AI Foundry
      mistral:
        description: mistral.ai
        title: Mistral
      moonshot:
        description: moonshot.ai
        title: Moonshot AI
      modelscope:
        description: modelscope.cn
        title: ModelScope
      novita:
        description: novita.ai
        title: Novita
      ollama:
        description: ollama.ai
        title: Ollama
      openai:
        description: openai.com
        title: OpenAI
      openai-compatible:
        description: OpenAI Compatible
        title: OpenAI Compatible
      openrouter:
        description: openrouter.ai
        title: OpenRouter
      perplexity:
        description: perplexity.ai
        title: Perplexity
      player2:
        description: player2.game
        title: Player2
      together:
        description: together.ai
        title: Together.ai
      google-generative-ai:
        description: gemini.google.com
        title: Google Gemini
      featherless:
        description: featherless.ai
        title: Featherless AI
      lm-studio:
        description: lmstudio.ai
        title: LM Studio
      vllm:
        description: vllm.ai
        title: vLLM
      volcengine:
        description: volcengine.com
        fields:
          field:
            appId:
              description: App ID of the project where you can obtain in Console
              label: App ID
        title: Volcano Engine
      xai:
        description: x.ai
        title: xAI
      transcriptions:
        playground:
          title: Transcription Playground
    title: Providers
  scene:
    description: Configure the environment where the character lives
    title: Scene
  system:
    color-scheme:
      description: Change the color scheme of the stage.
      title: Color Scheme
    developer:
      description: Some developer options.
      title: Developers
    general:
      description: Dark theme, languages, etc.
      title: General
    description: Customize your stage!
    sections:
      section:
        custom-color:
          fields:
            field:
              primary-color:
                label: Primary color
                rgb-on:
                  title: I Want It Dynamic!
          title: Color Scheme
        developer:
          title: Developer
          sections:
            section:
              use-magic-keys:
                title: useMagicKeys
                description: Test shortcuts
        theme-presets:
          presets:
            - colors:
                - AIRI Green
              description: The default greenish theme color, brought by AIRI to you!
              title: Default Color
            - colors:
                - Taupe
                - Beige
                - Ash Grey
                - Light Taupe
                - Ivory
                - Olive Grey
                - Sand
                - Warm Grey
              description: Soft, muted tones inspired by Giorgio Morandi's paintings
              title: Morandi Colors
            - colors:
                - Sky Blue
                - Mist
                - Sand
                - Moss Green
                - Water Lily
                - Wheat
                - Slate Blue
                - Sage
              description: Impressionist palette inspired by Claude Monet's works
              title: Monet Colors
            - colors:
                - Tan
                - Warm Taupe
                - Umber
                - Coffee
                - Bronze
                - Gold
                - Mustard
                - Amber
              description: Traditional Japanese color palette
              title: Japanese Colors
            - colors:
                - Nordic Blue
                - Ice
                - Fjord
                - Steel
                - Glacier
                - Slate
                - Cloud
                - Stone
              description: Scandinavian minimalist color scheme
              title: Nordic Colors
            - colors:
                - Rosy Dawn
                - Chinese Red
                - Smoky Brown
                - Bamboo Green
                - Dark Purple
                - Golden Yellow
                - Azure Blue
                - Ochre
              description: >-
                Traditional Chinese colors, derived from ancient textiles,
                porcelain and paintings
              title: Chinese Traditional Colors
          title: Color Scheme Presets
    title: System
sections:
  section:
    general:
      title: General
theme:
  title: Theme
  description: |
    Switch the base theme of AIRI, Light mode or Dark mode.
title: Settings
voices: Voice
vrm:
  change-model:
    from-file: Load from File
    from-file-select: Select
    from-url: Load from URL
    from-url-confirm: Load
    from-url-placeholder: Enter VRM model URL
    title: Change Model
  title: VRM Settings
  scale-and-position:
    model-info-title: Model Size Information
    model-info-x: Width (X)
    model-info-y: Height (Y)
    model-info-z: Depth (Z)
    tips: |
      Edit the initial position the VRM model.
      Coordinate axes are visualised.
    scale: Scale
    x: X Offset
    'y': Y Offset
    z: Z Offset
    fov: FOV (degree)
    rotation-y: Rotation (Y-axis)
    camera-distance: Camera distance
    eye-tracking-mode:
      title: Looking at
      options:
        option:
          camera: Camera
          mouse: Mouse
          disabled: Disabled
  switch-to-vrm:
    title: Switch to Live2D Avatar?
    change-to-vrm: Click here to switch to the Live2D avatar setting
  theme-color-from-model:
    button-extract:
      title: Extract
  skybox:
    skybox-intensity: SkyBox Intensity
    skybox-specular-mix: Specular Mix
