[package]
name = "airi"
version.workspace = true
description = "AIRI tamagotchi app built with <PERSON><PERSON>"
authors = [ "LemonNekoGH <<EMAIL>>" ]
license = "MIT"
repository = "https://github.com/moeru-ai/airi"
edition = "2024"
rust-version = "1.85.0"
publish = false

[features]
default = []

[lib]
name = "app_lib"
crate-type = [
  "staticlib",
  "cdylib",
  "rlib"
]

[build-dependencies]
tauri-build = { version = "2.4.0", features = [] }

[dependencies]
log = "0.4"
tauri = { version = "2.3.1", features = [
  'macos-private-api',
  'tray-icon',
  'image-png'
] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-os = "2"
tauri-plugin-mcp = { workspace = true }
tauri-plugin-ipc-audio-transcription-ort = { workspace = true }
tauri-plugin-ipc-audio-vad-ort = { workspace = true }
tauri-plugin-prevent-default = "1.3"
tauri-plugin-rdev = { workspace = true }
tauri-plugin-window-pass-through-on-hover = { workspace = true }
tauri-plugin-window-router-link = { workspace = true }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
tokio = "1.45.1"
ndarray = "0.16.1"
crossbeam-channel = "0.5.15"
anyhow = "1.0.98"
hf-hub = "0.4.3"
symphonia = "0.5.4"
rubato = "0.16.2"
byteorder = "1.5.0"
clap = { version = "4.5.40", features = ["derive"] }
tokenizers = "0.21.2"
url = "2.5.4"
tauri-plugin-window-state = "2.3.0"
tauri-plugin-positioner = "2.3.0"
specta = "=2.0.0-rc.22"
specta-typescript = "0.0.9"
tauri-specta = { version = "=2.0.0-rc.21", features = ["derive", "typescript"] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2.0.0"
